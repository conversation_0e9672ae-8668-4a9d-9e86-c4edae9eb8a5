const WebSocket = require('ws');

console.log('🧪 Testing Web UI Delayed Messages...');

const ws = new WebSocket('ws://localhost:3000');
let messageCount = 0;
let delayedThoughtCount = 0;

ws.on('open', function open() {
  console.log('✅ Connected to WebSocket');
});

ws.on('message', function message(data) {
  const msg = JSON.parse(data.toString());
  console.log(`📨 Received: ${msg.type}`);
  
  if (msg.type === 'connected') {
    console.log(`📨 Session ID: ${msg.sessionId}`);
    console.log('📤 Sending communication skills question...');
    ws.send(JSON.stringify({ 
      type: 'chat', 
      text: 'How can I improve my communication skills at work?' 
    }));
  } else if (msg.type === 'message') {
    messageCount++;
    console.log(`💬 Message ${messageCount}: ${msg.character} - ${msg.text.substring(0, 50)}...`);
  } else if (msg.type === 'delayed_thought') {
    delayedThoughtCount++;
    console.log(`💭 DELAYED THOUGHT ${delayedThoughtCount}: ${msg.character} - ${msg.text.substring(0, 50)}...`);
  } else if (msg.type === 'chat_complete') {
    console.log('✅ Initial chat complete');
    console.log('⏳ Waiting for delayed thoughts...');
  } else if (msg.type === 'delayed_thoughts_available') {
    console.log(`🔔 ${msg.count} delayed thoughts available`);
  } else if (msg.type === 'typing_start') {
    console.log(`⌨️  ${msg.character} is typing...`);
  } else if (msg.type === 'typing_stop') {
    console.log(`⏹️  ${msg.character} stopped typing`);
  } else {
    console.log(`📨 Other message: ${JSON.stringify(msg)}`);
  }
});

ws.on('close', function close() {
  console.log('❌ WebSocket connection closed');
  console.log(`📊 Summary: ${messageCount} initial messages, ${delayedThoughtCount} delayed thoughts`);
});

ws.on('error', function error(err) {
  console.error('❌ WebSocket error:', err);
});

// Close after 2 minutes
setTimeout(() => {
  console.log('⏰ Test timeout - closing connection...');
  ws.close();
}, 120000);
