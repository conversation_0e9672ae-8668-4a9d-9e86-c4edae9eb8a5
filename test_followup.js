const WebSocket = require('ws');

// Connect to the WebSocket server
const ws = new WebSocket('ws://localhost:3000');

ws.on('open', function open() {
  console.log('Connected to WebSocket server');
  
  // Send a test message after a short delay
  setTimeout(() => {
    console.log('Sending test message...');
    ws.send(JSON.stringify({
      type: 'chat',
      text: 'Hello, can you tell me about yourself?'
    }));
  }, 1000);
});

ws.on('message', function message(data) {
  const msg = JSON.parse(data.toString());
  console.log('Received message:', JSON.stringify(msg, null, 2));
});

ws.on('close', function close() {
  console.log('WebSocket connection closed');
});

ws.on('error', function error(err) {
  console.error('WebSocket error:', err);
});

// Keep the script running for 2 minutes to catch follow-up messages
setTimeout(() => {
  console.log('Test completed, closing connection...');
  ws.close();
  process.exit(0);
}, 120000); // 2 minutes
