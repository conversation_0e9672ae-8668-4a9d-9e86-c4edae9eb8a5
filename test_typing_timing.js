const WebSocket = require('ws');

// Connect to the WebSocket server
const ws = new WebSocket('ws://localhost:3000');

let messageStartTime = null;
let typingStartTime = null;

ws.on('open', function open() {
  console.log('Connected to WebSocket server');
  
  // Send a test message after a short delay
  setTimeout(() => {
    console.log('Sending test message...');
    messageStartTime = Date.now();
    ws.send(JSON.stringify({
      type: 'chat',
      text: 'Can you help me with communication skills?'
    }));
  }, 1000);
});

ws.on('message', function message(data) {
  const msg = JSON.parse(data.toString());
  const currentTime = Date.now();
  
  if (msg.type === 'typing_start') {
    typingStartTime = currentTime;
    const timeSinceMessage = currentTime - messageStartTime;
    console.log(`🟡 TYPING STARTED: ${timeSinceMessage}ms after user message sent`);
    console.log(`   Character: ${msg.character}`);
  } else if (msg.type === 'typing_stop') {
    const timeSinceMessage = currentTime - messageStartTime;
    const typingDuration = typingStartTime ? currentTime - typingStartTime : 0;
    console.log(`🔴 TYPING STOPPED: ${timeSinceMessage}ms after user message sent`);
    console.log(`   Typing duration: ${typingDuration}ms`);
  } else if (msg.type === 'message') {
    const timeSinceMessage = currentTime - messageStartTime;
    console.log(`💬 MESSAGE RECEIVED: ${timeSinceMessage}ms after user message sent`);
    console.log(`   Character: ${msg.character}`);
    console.log(`   Text: ${msg.text.substring(0, 50)}...`);
  } else if (msg.type === 'delayed_thought') {
    const timeSinceMessage = currentTime - messageStartTime;
    console.log(`💭 DELAYED THOUGHT: ${timeSinceMessage}ms after user message sent`);
    console.log(`   Character: ${msg.character}`);
    console.log(`   Text: ${msg.text.substring(0, 50)}...`);
  } else {
    console.log(`📨 Other message type: ${msg.type}`);
  }
});

ws.on('close', function close() {
  console.log('WebSocket connection closed');
});

ws.on('error', function error(err) {
  console.error('WebSocket error:', err);
});

// Close after 30 seconds
setTimeout(() => {
  console.log('Closing connection...');
  ws.close();
}, 30000);
