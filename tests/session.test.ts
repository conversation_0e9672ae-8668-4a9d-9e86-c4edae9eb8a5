import { DBOS } from '@dbos-inc/dbos-sdk';
import { SessionService } from '../src/core/SessionService';
import { ConversationService } from '../src/core/ConversationService';
import { SessionCleanupService } from '../src/core/SessionCleanupService';
import { ForaChat } from '../src/operations';
import { Session, SessionCreateRequest, Conversation } from '../src/models/types';

describe('Session Management', () => {
  beforeAll(async () => {
    // Initialize DBOS for testing
    DBOS.setConfig({
      name: "forachat-test",
      databaseUrl: process.env.DATABASE_URL || "postgresql://localhost:5432/forachat_test",
      userDbclient: "knex" as any
    });
    await DBOS.launch();
  });

  afterAll(async () => {
    // Clean up test data
    await DBOS.knexClient('forachat.sessions').del();
    await DBOS.knexClient('forachat.messages').del();
    await DBOS.knexClient('forachat.conversations').del();
  });

  beforeEach(async () => {
    // Clean up before each test
    await DBOS.knexClient('forachat.sessions').del();
    await DBOS.knexClient('forachat.messages').del();
    await DBOS.knexClient('forachat.conversations').del();
  });

  describe('SessionService', () => {
    test('should create a new session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_123',
        channel: 'web',
        metadata: { test: 'data' }
      };

      const session = await SessionService.createSession(sessionRequest);

      expect(session).toBeDefined();
      expect(session.id).toBeDefined();
      expect(session.user_identifier).toBe('test_user_123');
      expect(session.channel).toBe('web');
      expect(session.metadata).toEqual({ test: 'data' });
      expect(session.expires_at).toBeDefined();
    });

    test('should get session by ID', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_456',
        channel: 'repl'
      };

      const createdSession = await SessionService.createSession(sessionRequest);
      const retrievedSession = await SessionService.getSession(createdSession.id);

      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.id).toBe(createdSession.id);
      expect(retrievedSession!.user_identifier).toBe('test_user_456');
      expect(retrievedSession!.channel).toBe('repl');
    });

    test('should get session by user and channel', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_789',
        channel: 'sms'
      };

      const createdSession = await SessionService.createSession(sessionRequest);
      const retrievedSession = await SessionService.getSessionByUserAndChannel('test_user_789', 'sms');

      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.id).toBe(createdSession.id);
    });

    test('should update session activity', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_activity',
        channel: 'web'
      };

      const session = await SessionService.createSession(sessionRequest);
      const originalActivity = session.last_activity;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 100));

      await SessionService.updateSessionActivity(session.id);
      const updatedSession = await SessionService.getSession(session.id);

      expect(updatedSession!.last_activity.getTime()).toBeGreaterThan(originalActivity.getTime());
    });

    test('should update session conversation', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_conv',
        channel: 'web'
      };

      const session = await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversation();

      await SessionService.updateSessionConversation(session.id, conversation.id);
      const updatedSession = await SessionService.getSession(session.id);

      expect(updatedSession!.conversation_id).toBe(conversation.id);
    });

    test('should delete session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_delete',
        channel: 'web'
      };

      const session = await SessionService.createSession(sessionRequest);
      await SessionService.deleteSession(session.id);
      const deletedSession = await SessionService.getSession(session.id);

      expect(deletedSession).toBeNull();
    });

    test('should cleanup expired sessions', async () => {
      // Create an expired session
      const expiredSessionRequest: SessionCreateRequest = {
        userIdentifier: 'expired_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000) // Expired 1 second ago
      };

      // Create a valid session
      const validSessionRequest: SessionCreateRequest = {
        userIdentifier: 'valid_user',
        channel: 'web'
      };

      await SessionService.createSession(expiredSessionRequest);
      await SessionService.createSession(validSessionRequest);

      const deletedCount = await SessionService.cleanupExpiredSessions();

      expect(deletedCount).toBe(1);

      // Verify only the valid session remains
      const validSession = await SessionService.getSessionByUserAndChannel('valid_user', 'web');
      const expiredSession = await SessionService.getSessionByUserAndChannel('expired_user', 'web');

      expect(validSession).toBeDefined();
      expect(expiredSession).toBeNull();
    });

    test('should extend session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_extend',
        channel: 'web'
      };

      const session = await SessionService.createSession(sessionRequest);
      const originalExpiry = session.expires_at!;

      await SessionService.extendSession(session.id, 48); // Extend by 48 hours
      const extendedSession = await SessionService.getSession(session.id);

      expect(extendedSession!.expires_at!.getTime()).toBeGreaterThan(originalExpiry.getTime());
    });
  });

  describe('Session-Conversation Integration', () => {
    test('should create conversation for session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_conv_create',
        channel: 'web'
      };

      const session = await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversationForSession(session.id);

      expect(conversation).toBeDefined();
      expect(conversation.id).toBeDefined();

      // Verify session was updated with conversation ID
      const updatedSession = await SessionService.getSession(session.id);
      expect(updatedSession!.conversation_id).toBe(conversation.id);
    });

    test('should get conversation by session', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'test_user_get_conv',
        channel: 'web'
      };

      const session = await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversationForSession(session.id);

      const retrievedConversation = await ConversationService.getConversationBySession(session.id);

      expect(retrievedConversation).toBeDefined();
      expect(retrievedConversation!.id).toBe(conversation.id);
    });

    test('should get sessions for conversation', async () => {
      const conversation = await ConversationService.createConversation();

      // Create multiple sessions for the same conversation
      const session1Request: SessionCreateRequest = {
        userIdentifier: 'user1',
        channel: 'web',
        conversationId: conversation.id
      };

      const session2Request: SessionCreateRequest = {
        userIdentifier: 'user2',
        channel: 'repl',
        conversationId: conversation.id
      };

      await SessionService.createSession(session1Request);
      await SessionService.createSession(session2Request);

      const sessions = await ConversationService.getSessionsForConversation(conversation.id);

      expect(sessions).toHaveLength(2);
      expect(sessions.map(s => s.user_identifier)).toContain('user1');
      expect(sessions.map(s => s.user_identifier)).toContain('user2');
    });

    test('should get active sessions for user', async () => {
      const userIdentifier = 'multi_session_user';

      // Create multiple sessions for the same user
      const webSessionRequest: SessionCreateRequest = {
        userIdentifier,
        channel: 'web'
      };

      const replSessionRequest: SessionCreateRequest = {
        userIdentifier,
        channel: 'repl'
      };

      const webSession = await SessionService.createSession(webSessionRequest);
      const replSession = await SessionService.createSession(replSessionRequest);

      // Add conversations to make them "active"
      const conversation1 = await ConversationService.createConversation();
      const conversation2 = await ConversationService.createConversation();

      await SessionService.updateSessionConversation(webSession.id, conversation1.id);
      await SessionService.updateSessionConversation(replSession.id, conversation2.id);

      const activeSessions = await ConversationService.getActiveSessionsForUser(userIdentifier);

      expect(activeSessions).toHaveLength(2);
      expect(activeSessions.map(s => s.channel)).toContain('web');
      expect(activeSessions.map(s => s.channel)).toContain('repl');
    });
  });

  describe('Session Cleanup', () => {
    test('should get cleanup stats', async () => {
      // Create test data
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'stats_user',
        channel: 'web'
      };

      await SessionService.createSession(sessionRequest);
      const conversation = await ConversationService.createConversation();
      await ConversationService.addMessage('user', 'test message', conversation.id);

      const stats = await SessionCleanupService.getCleanupStats();

      expect(stats.totalSessions).toBeGreaterThan(0);
      expect(stats.totalConversations).toBeGreaterThan(0);
      expect(stats.totalMessages).toBeGreaterThan(0);
    });

    test('should perform manual cleanup', async () => {
      // Create expired session
      const expiredSessionRequest: SessionCreateRequest = {
        userIdentifier: 'cleanup_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000)
      };

      await SessionService.createSession(expiredSessionRequest);

      const result = await SessionCleanupService.manualCleanup({
        cleanupExpiredSessions: true,
        cleanupOrphanedConversations: false,
        cleanupOldMessages: false,
        cleanupMessageQueue: false
      });

      expect(result.expiredSessions).toBe(1);
    });
  });

  describe('ForaChat Operations Integration', () => {
    test('should create session through ForaChat operations', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'forachat_user',
        channel: 'web'
      };

      const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
      const session = await handle.getResult();

      expect(session).toBeDefined();
      expect(session.user_identifier).toBe('forachat_user');
    });

    test('should get session through ForaChat operations', async () => {
      const sessionRequest: SessionCreateRequest = {
        userIdentifier: 'forachat_get_user',
        channel: 'web'
      };

      const createHandle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
      const createdSession = await createHandle.getResult();

      const getHandle = await DBOS.startWorkflow(ForaChat).getSession(createdSession.id);
      const retrievedSession = await getHandle.getResult();

      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.id).toBe(createdSession.id);
    });

    test('should cleanup sessions through ForaChat operations', async () => {
      const expiredSessionRequest: SessionCreateRequest = {
        userIdentifier: 'forachat_cleanup_user',
        channel: 'web',
        expiresAt: new Date(Date.now() - 1000)
      };

      const createHandle = await DBOS.startWorkflow(ForaChat).createSession(expiredSessionRequest);
      await createHandle.getResult();

      const cleanupHandle = await DBOS.startWorkflow(ForaChat).cleanupExpiredSessions();
      const deletedCount = await cleanupHandle.getResult();

      expect(deletedCount).toBe(1);
    });
  });
});
