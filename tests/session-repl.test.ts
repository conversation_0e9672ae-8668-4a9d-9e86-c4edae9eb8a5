import { DBOS } from '@dbos-inc/dbos-sdk';
import { REPLInterface } from '../src/interfaces/REPLInterface';
import { SessionService } from '../src/core/SessionService';
import { ConversationService } from '../src/core/ConversationService';
import { ForaChatApp } from '../src/ForaChatApp';
import http from 'http';

describe('REPL Session Handling', () => {
  let app: ForaChatApp;
  let server: http.Server;
  let serverPort: number;

  beforeAll(async () => {
    // Initialize DBOS for testing
    DBOS.setConfig({
      name: "forachat-test-repl",
      databaseUrl: process.env.DATABASE_URL || "postgresql://localhost:5432/forachat_test",
      userDbclient: "knex" as any
    });

    app = new ForaChatApp();
    await app.initialize();
    
    // Start server on a random port for testing
    const expressApp = app.getWebInterface().getApp();
    server = http.createServer(expressApp);
    
    await new Promise<void>((resolve) => {
      server.listen(0, () => {
        serverPort = (server.address() as any).port;
        resolve();
      });
    });
  });

  afterAll(async () => {
    // Clean up test data
    await DBOS.knexClient('forachat.sessions').delete();
    await DBOS.knexClient('forachat.messages').delete();
    await DBOS.knexClient('forachat.conversations').delete();
    
    // Close server
    server.close();
  });

  beforeEach(async () => {
    // Clean up before each test
    await DBOS.knexClient('forachat.sessions').delete();
    await DBOS.knexClient('forachat.messages').delete();
    await DBOS.knexClient('forachat.conversations').delete();
  });

  describe('REPL Session Creation', () => {
    test('should create new session when no session ID provided', async () => {
      const repl = new REPLInterface('localhost', serverPort);
      
      // Mock the session creation by directly testing the underlying logic
      const sessionRequest = {
        userIdentifier: 'repl_test_user',
        channel: 'repl' as const,
        metadata: {
          hostname: 'test-host',
          username: 'test-user',
          platform: 'test-platform'
        }
      };

      const session = await SessionService.createSession(sessionRequest);
      
      expect(session).toBeDefined();
      expect(session.user_identifier).toBe('repl_test_user');
      expect(session.channel).toBe('repl');
      expect(session.metadata).toEqual(sessionRequest.metadata);
    });

    test('should restore existing session when session ID provided', async () => {
      // Create a session first
      const existingSession = await SessionService.createSession({
        userIdentifier: 'repl_existing_user',
        channel: 'repl'
      });

      // Create conversation and add messages
      const conversation = await ConversationService.createConversation();
      await SessionService.updateSessionConversation(existingSession.id, conversation.id);
      await ConversationService.addMessage('user', 'Previous message', conversation.id);
      await ConversationService.addMessage('Fora', 'Previous response', conversation.id);

      // Test session restoration
      const repl = new REPLInterface('localhost', serverPort, existingSession.id);
      
      // Verify the session can be retrieved
      const retrievedSession = await SessionService.getSession(existingSession.id);
      expect(retrievedSession).toBeDefined();
      expect(retrievedSession!.conversation_id).toBe(conversation.id);

      // Verify conversation history exists
      const messages = await ConversationService.getConversationMessages(conversation.id);
      expect(messages).toHaveLength(2);
      expect(messages[0].text).toBe('Previous message');
      expect(messages[1].text).toBe('Previous response');
    });

    test('should handle invalid session ID gracefully', async () => {
      const repl = new REPLInterface('localhost', serverPort, 'invalid-session-id');
      
      // Test that invalid session ID doesn't break the system
      const session = await SessionService.getSession('invalid-session-id');
      expect(session).toBeNull();
    });
  });

  describe('REPL Session Management', () => {
    test('should update session activity on message send', async () => {
      const session = await SessionService.createSession({
        userIdentifier: 'repl_activity_user',
        channel: 'repl'
      });

      const originalActivity = session.last_activity;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 100));

      // Simulate activity update
      await SessionService.updateSessionActivity(session.id);
      
      const updatedSession = await SessionService.getSession(session.id);
      expect(updatedSession!.last_activity.getTime()).toBeGreaterThan(originalActivity.getTime());
    });

    test('should link conversation to session', async () => {
      const session = await SessionService.createSession({
        userIdentifier: 'repl_conversation_user',
        channel: 'repl'
      });

      const conversation = await ConversationService.createConversation();
      await SessionService.updateSessionConversation(session.id, conversation.id);

      const updatedSession = await SessionService.getSession(session.id);
      expect(updatedSession!.conversation_id).toBe(conversation.id);

      // Verify conversation can be retrieved by session
      const retrievedConversation = await ConversationService.getConversationBySession(session.id);
      expect(retrievedConversation).toBeDefined();
      expect(retrievedConversation!.id).toBe(conversation.id);
    });

    test('should handle multiple REPL sessions for same user', async () => {
      const userIdentifier = 'repl_multi_user';

      // Create multiple REPL sessions (simulating different terminals/machines)
      const session1 = await SessionService.createSession({
        userIdentifier: `repl_${userIdentifier}_machine1`,
        channel: 'repl',
        metadata: { hostname: 'machine1' }
      });

      const session2 = await SessionService.createSession({
        userIdentifier: `repl_${userIdentifier}_machine2`,
        channel: 'repl',
        metadata: { hostname: 'machine2' }
      });

      expect(session1.id).not.toBe(session2.id);
      expect(session1.metadata).toEqual({ hostname: 'machine1' });
      expect(session2.metadata).toEqual({ hostname: 'machine2' });
    });
  });

  describe('REPL Session Persistence', () => {
    test('should persist conversation across REPL sessions', async () => {
      // Create initial session and conversation
      const session = await SessionService.createSession({
        userIdentifier: 'repl_persist_user',
        channel: 'repl'
      });

      const conversation = await ConversationService.createConversation();
      await SessionService.updateSessionConversation(session.id, conversation.id);
      
      // Add some messages
      await ConversationService.addMessage('user', 'First message', conversation.id);
      await ConversationService.addMessage('Fora', 'First response', conversation.id);

      // Simulate REPL restart with same session ID
      const restoredSession = await SessionService.getSession(session.id);
      expect(restoredSession).toBeDefined();
      expect(restoredSession!.conversation_id).toBe(conversation.id);

      // Verify conversation history is intact
      const messages = await ConversationService.getConversationMessages(conversation.id);
      expect(messages).toHaveLength(2);
      expect(messages[0].character).toBe('user');
      expect(messages[1].character).toBe('Fora');
    });

    test('should handle session expiration', async () => {
      // Create expired session
      const expiredSession = await SessionService.createSession({
        userIdentifier: 'repl_expired_user',
        channel: 'repl',
        expiresAt: new Date(Date.now() - 1000) // Expired 1 second ago
      });

      // Attempt to get expired session
      const retrievedSession = await SessionService.getSessionByUserAndChannel(
        'repl_expired_user', 
        'repl'
      );

      // Should not find expired session
      expect(retrievedSession).toBeNull();
    });

    test('should extend session on activity', async () => {
      const session = await SessionService.createSession({
        userIdentifier: 'repl_extend_user',
        channel: 'repl'
      });

      const originalExpiry = session.expires_at!;

      // Extend session
      await SessionService.extendSession(session.id, 24);

      const extendedSession = await SessionService.getSession(session.id);
      expect(extendedSession!.expires_at!.getTime()).toBeGreaterThan(originalExpiry.getTime());
    });
  });

  describe('REPL Command Line Arguments', () => {
    test('should parse session ID from command line arguments', () => {
      // Mock process.argv
      const originalArgv = process.argv;
      process.argv = ['node', 'repl.js', '--session-id', 'test-session-123'];

      // Test argument parsing logic (this would be in the actual repl.ts file)
      const args = process.argv.slice(2);
      let sessionId: string | undefined;

      for (let i = 0; i < args.length; i++) {
        if (args[i] === '--session-id' && i + 1 < args.length) {
          sessionId = args[i + 1];
          break;
        }
      }

      expect(sessionId).toBe('test-session-123');

      // Restore original argv
      process.argv = originalArgv;
    });

    test('should parse server URL and port from command line arguments', () => {
      const originalArgv = process.argv;
      process.argv = [
        'node', 'repl.js', 
        '--server-url', 'example.com',
        '--server-port', '8080',
        '--session-id', 'test-session'
      ];

      const args = process.argv.slice(2);
      const result: { sessionId?: string; serverUrl?: string; serverPort?: number } = {};

      for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        
        if (arg === '--session-id' && i + 1 < args.length) {
          result.sessionId = args[i + 1];
          i++;
        } else if (arg === '--server-url' && i + 1 < args.length) {
          result.serverUrl = args[i + 1];
          i++;
        } else if (arg === '--server-port' && i + 1 < args.length) {
          result.serverPort = parseInt(args[i + 1]);
          i++;
        }
      }

      expect(result.sessionId).toBe('test-session');
      expect(result.serverUrl).toBe('example.com');
      expect(result.serverPort).toBe(8080);

      process.argv = originalArgv;
    });
  });

  describe('REPL Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      // Create REPL with invalid server
      const repl = new REPLInterface('invalid-host', 9999);
      
      // This should not throw but handle the error gracefully
      // In a real implementation, this would be tested by mocking the HTTP request
      expect(() => {
        new REPLInterface('invalid-host', 9999);
      }).not.toThrow();
    });

    test('should handle session creation failure', async () => {
      // Test with invalid session request
      try {
        await SessionService.createSession({
          userIdentifier: '',
          channel: 'repl'
        });
      } catch (error) {
        // Should handle validation errors appropriately
        expect(error).toBeDefined();
      }
    });

    test('should handle conversation retrieval failure', async () => {
      const session = await SessionService.createSession({
        userIdentifier: 'repl_no_conv_user',
        channel: 'repl'
      });

      // Try to get conversation for session without one
      const conversation = await ConversationService.getConversationBySession(session.id);
      expect(conversation).toBeNull();
    });
  });
});
