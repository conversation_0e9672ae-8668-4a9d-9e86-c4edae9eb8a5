// Test setup file
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Mock DBOS logger to prevent console spam during tests
jest.mock('@dbos-inc/dbos-sdk', () => ({
  DBOS: {
    logger: {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn(),
    },
    transaction: () => (target: any, propertyName: string, descriptor: PropertyDescriptor) => descriptor,
    step: () => (target: any, propertyName: string, descriptor: PropertyDescriptor) => descriptor,
    workflow: () => (target: any, propertyName: string, descriptor: PropertyDescriptor) => descriptor,
    knexClient: jest.fn().mockImplementation(() => ({
      where: jest.fn().mockReturnThis(),
      count: jest.fn().mockReturnThis(),
      first: jest.fn().mockResolvedValue({ count: '0' }),
      select: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      returning: jest.fn().mockResolvedValue([]),
      update: jest.fn().mockReturnThis(),
      del: jest.fn().mockReturnThis(),
    })),
    setConfig: jest.fn(),
    launch: jest.fn(),
    startWorkflow: jest.fn(),
  },
  WorkflowQueue: jest.fn().mockImplementation((name: string, options: any) => ({
    name,
    options,
  })),
}));

// Mock Google Generative AI
jest.mock('@google/generative-ai', () => ({
  GoogleGenerativeAI: jest.fn(),
}));

// Mock the new services
jest.mock('../src/services/GeminiLLMService', () => ({
  GeminiLLMService: jest.fn().mockImplementation(() => ({
    generate: jest.fn(),
  })),
}));

jest.mock('../src/core/PromptService', () => ({
  PromptService: {
    getSystemPrompt: jest.fn(),
    getAvailablePrompts: jest.fn(),
    clearCache: jest.fn(),
    getCacheSize: jest.fn(),
  },
}));

jest.mock('../src/core/ConversationService', () => ({
  ConversationService: {
    createConversation: jest.fn(),
    addMessage: jest.fn(),
    checkMessageExists: jest.fn(),
    getConversationMessages: jest.fn(),
    getConversation: jest.fn(),
    deleteConversation: jest.fn(),
    getRecentConversations: jest.fn(),
    getDelayedThoughts: jest.fn(),
    updateUserActivity: jest.fn(),
    updateEngagementLevel: jest.fn(),
    getLastUserActivity: jest.fn(),
  },
}));

jest.mock('../src/core/MessageQueueService', () => ({
  MessageQueueService: {
    enqueueMessage: jest.fn(),
    getPendingMessages: jest.fn(),
    getReadyMessages: jest.fn(),
    updateMessageStatus: jest.fn(),
    checkSimilarity: jest.fn(),
    cancelMessage: jest.fn(),
    cancelAllMessages: jest.fn(),
    cleanupOldMessages: jest.fn(),
    getQueueStats: jest.fn(),
    resetStuckMessages: jest.fn(),
    getQueueDebugInfo: jest.fn(),
    forceProcessReadyMessages: jest.fn(),
  },
}));

jest.mock('../src/core/ConversationDecayService', () => ({
  ConversationDecayService: {
    calculateDelayMultiplier: jest.fn(),
    calculateEngagementLevel: jest.fn(),
    updateConversationEngagement: jest.fn(),
    applyDecayToDelay: jest.fn(),
    getDecayStatus: jest.fn(),
    shouldTimeoutConversation: jest.fn(),
  },
}));

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.GEMINI_API_KEY = 'test-api-key';
process.env.DBOS_DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
