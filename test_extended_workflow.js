const WebSocket = require('ws');

// Test the extended workflow functionality
async function testExtendedWorkflow() {
    console.log('🧪 Testing Extended Workflow...');
    
    const ws = new WebSocket('ws://localhost:3000');
    
    ws.on('open', () => {
        console.log('✅ Connected to WebSocket server');
        
        // Send a test message to trigger the workflow
        console.log('📤 Sending test message...');
        ws.send(JSON.stringify({
            type: 'chat',
            text: 'I\'m having trouble communicating with my team members. How can I improve my communication skills?'
        }));
    });
    
    ws.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        switch (message.type) {
            case 'connected':
                console.log(`🔗 Session ID: ${message.sessionId}`);
                break;
                
            case 'chat_start':
                console.log(`🎬 Chat started: ${message.theme}`);
                break;
                
            case 'message':
                console.log(`💬 ${message.character}: ${message.text}`);
                break;
                
            case 'chat_complete':
                console.log('✅ Initial chat complete');
                console.log(`🎯 Skills: ${message.skills?.join(', ') || 'None'}`);
                break;
                
            case 'extended_workflow_start':
                console.log('🚀 Extended workflow started!');
                console.log(`⏱️  Duration: ${message.duration}ms (${message.duration / 1000 / 60} minutes)`);
                console.log(`📝 ${message.message}`);
                break;
                
            case 'extended_workflow_message':
                console.log(`🔄 Extended: ${message.character}: ${message.text}`);
                if (message.timeRemaining) {
                    const minutesRemaining = Math.floor(message.timeRemaining / 1000 / 60);
                    const secondsRemaining = Math.floor((message.timeRemaining % 60000) / 1000);
                    console.log(`⏰ Time remaining: ${minutesRemaining}m ${secondsRemaining}s`);
                }
                break;
                
            case 'extended_workflow_end':
                console.log('🏁 Extended workflow ended');
                console.log(`📝 ${message.message}`);
                break;

            case 'extended_workflow_extended':
                console.log('🔄 Extended workflow timeout extended!');
                console.log(`📝 ${message.message}`);
                console.log(`⏱️  New duration: ${message.newDuration}ms`);
                console.log(`⏰ Time remaining: ${message.timeRemaining}ms`);
                break;

            case 'autonomous_message':
                console.log(`🤖 Autonomous: ${message.character}: ${message.text}`);
                break;
                
            case 'interrupted':
                console.log(`⚠️  Interrupted: ${message.message}`);
                if (message.extendedWorkflowInterrupted) {
                    console.log('🔄 Extended workflow was interrupted');
                }
                break;
                
            case 'error':
                console.error(`❌ Error: ${message.error}`);
                if (message.details) {
                    console.error(`📋 Details: ${message.details}`);
                }
                break;
                
            default:
                console.log(`📨 Unknown message type: ${message.type}`, message);
        }
    });
    
    ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
    });
    
    ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });
    
    // Test timeout extension after 5 minutes
    setTimeout(() => {
        console.log('🔄 Testing timeout extension with new message...');
        ws.send(JSON.stringify({
            type: 'chat',
            text: 'This is helpful! Can you give me more tips on active listening?'
        }));
    }, 300000); // 5 minutes

    // Test another extension after 8 minutes
    setTimeout(() => {
        console.log('🔄 Testing another timeout extension...');
        ws.send(JSON.stringify({
            type: 'chat',
            text: 'What about handling difficult conversations with colleagues?'
        }));
    }, 480000); // 8 minutes

    // Test interruption after 12 minutes
    setTimeout(() => {
        console.log('🔄 Testing interruption...');
        ws.send(JSON.stringify({
            type: 'interrupt',
            text: 'Actually, I want to focus on conflict resolution skills instead.'
        }));
    }, 720000); // 12 minutes

    // Keep the test running for 20 minutes to see the full extended workflow with extensions
    setTimeout(() => {
        console.log('⏰ Test completed after 20 minutes');
        ws.close();
        process.exit(0);
    }, 1200000); // 20 minutes
}

// Run the test
testExtendedWorkflow().catch(console.error);
