const WebSocket = require('ws');

// Test script to verify conversation decay functionality
async function testDecayFunctionality() {
    console.log('🧪 Testing Conversation Decay Functionality...');
    
    const ws = new WebSocket('ws://localhost:3000');
    let sessionId = null;
    let messageCount = 0;
    
    ws.on('open', () => {
        console.log('✅ Connected to WebSocket server');
        
        // Send initial message to start conversation
        console.log('📤 Sending initial message...');
        ws.send(JSON.stringify({
            type: 'chat',
            text: 'I want to improve my communication skills.'
        }));
    });
    
    ws.on('message', (data) => {
        const message = JSON.parse(data.toString());
        
        switch (message.type) {
            case 'connected':
                sessionId = message.sessionId;
                console.log(`🔗 Session ID: ${sessionId}`);
                break;
                
            case 'chat_start':
                console.log(`🎬 Chat started: ${message.theme}`);
                break;
                
            case 'message':
                messageCount++;
                console.log(`💬 [${messageCount}] ${message.character}: ${message.text.substring(0, 80)}...`);
                break;
                
            case 'chat_complete':
                console.log('✅ Initial chat complete - characters will now continue with decay logic');
                console.log('⏳ Waiting to observe decay behavior...');
                
                // Test 1: Wait 3 minutes to see moderate decay
                setTimeout(() => {
                    console.log('\n🔄 Test 1: Sending message after 3 minutes (should see moderate decay)');
                    ws.send(JSON.stringify({
                        type: 'chat',
                        text: 'Can you give me more specific examples?'
                    }));
                }, 3 * 60 * 1000); // 3 minutes
                
                // Test 2: Wait 8 minutes to see heavy decay
                setTimeout(() => {
                    console.log('\n🔄 Test 2: Sending message after 8 minutes (should see heavy decay)');
                    ws.send(JSON.stringify({
                        type: 'chat',
                        text: 'What about handling difficult conversations?'
                    }));
                }, 8 * 60 * 1000); // 8 minutes
                
                // Test 3: Wait 16 minutes to see timeout
                setTimeout(() => {
                    console.log('\n🔄 Test 3: Waiting 16 minutes (should see conversation timeout)');
                    // Don't send a message - just wait for timeout
                }, 16 * 60 * 1000); // 16 minutes
                break;
                
            case 'delayed_thought':
                console.log(`💭 Delayed thought from ${message.character}: ${message.text.substring(0, 80)}...`);
                break;
                
            case 'extended_workflow_start':
                console.log('🚀 Extended workflow started!');
                break;
                
            case 'extended_workflow_message':
                console.log(`🔄 Extended: ${message.character}: ${message.text.substring(0, 80)}...`);
                break;
                
            case 'extended_workflow_end':
                console.log('🏁 Extended workflow ended');
                break;
                
            case 'conversation_timeout':
                console.log('⏰ SUCCESS! Conversation timed out due to decay!');
                console.log(`📝 ${message.message}`);
                
                // Test completed successfully
                setTimeout(() => {
                    console.log('✅ Decay test completed successfully!');
                    ws.close();
                    process.exit(0);
                }, 2000);
                break;
                
            case 'autonomous_message':
                console.log(`🤖 Autonomous: ${message.character}: ${message.text.substring(0, 80)}...`);
                break;
                
            case 'typing_start':
                console.log(`⌨️  ${message.character} is typing...`);
                break;
                
            case 'typing_stop':
                // Don't log typing stop to reduce noise
                break;
                
            case 'error':
                console.error(`❌ Error: ${message.error}`);
                if (message.details) {
                    console.error(`📋 Details: ${message.details}`);
                }
                break;
                
            default:
                console.log(`📨 ${message.type}:`, message);
        }
    });
    
    ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
    });
    
    ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });
    
    // Timeout after 20 minutes if test doesn't complete
    setTimeout(() => {
        console.log('⏰ Test timeout after 20 minutes');
        ws.close();
        process.exit(1);
    }, 20 * 60 * 1000); // 20 minutes
}

// Helper function to format time
function formatTime(ms) {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
}

// Run the test
console.log('🚀 Starting decay functionality test...');
console.log('📋 Test plan:');
console.log('  1. Start conversation');
console.log('  2. Wait 3 minutes - expect moderate decay');
console.log('  3. Wait 8 minutes - expect heavy decay');
console.log('  4. Wait 16 minutes - expect conversation timeout');
console.log('');

testDecayFunctionality().catch(console.error);
