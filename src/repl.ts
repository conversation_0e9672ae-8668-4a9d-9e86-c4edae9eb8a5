import { REPLInterface } from './interfaces/REPLInterface';

function parseArgs(): { sessionId?: string; serverUrl?: string; serverPort?: number } {
    const args = process.argv.slice(2);
    const result: { sessionId?: string; serverUrl?: string; serverPort?: number } = {};

    for (let i = 0; i < args.length; i++) {
        const arg = args[i];

        if (arg === '--session-id' && i + 1 < args.length) {
            result.sessionId = args[i + 1];
            i++; // Skip next argument as it's the value
        } else if (arg === '--server-url' && i + 1 < args.length) {
            result.serverUrl = args[i + 1];
            i++;
        } else if (arg === '--server-port' && i + 1 < args.length) {
            result.serverPort = parseInt(args[i + 1]);
            i++;
        } else if (arg === '--help' || arg === '-h') {
            console.log('ForaChat REPL');
            console.log('Usage: npm run repl [options]');
            console.log('');
            console.log('Options:');
            console.log('  --session-id <id>     Resume a previous session');
            console.log('  --server-url <url>    Server URL (default: localhost)');
            console.log('  --server-port <port>  Server port (default: 3000)');
            console.log('  --help, -h            Show this help message');
            process.exit(0);
        }
    }

    return result;
}

async function main() {
    try {
        const args = parseArgs();
        const repl = new REPLInterface(
            args.serverUrl || 'localhost',
            args.serverPort || 3000,
            args.sessionId
        );
        await repl.start();
    } catch (error) {
        console.error('❌ REPL failed to start:', error);
        process.exit(1);
    }
}

main();
