import { DBOS, WorkflowQueue } from '@dbos-inc/dbos-sdk';
import { ConversationService } from './core/ConversationService';
import { PromptService } from './core/PromptService';
import { MessageQueueService } from './core/MessageQueueService';
import { ConversationDecayService } from './core/ConversationDecayService';
import { GeminiLLMService } from './services/GeminiLLMService';
import { LLMService } from './services/LLMService';
import { Conversation, Message, DelayedMessage } from './models/types';

// Character queue for background thoughts - initialized at module load time
export const characterQueue = new WorkflowQueue("character_thoughts", { concurrency: 3 });

// Track active character workflows to prevent duplicates
const activeCharacterWorkflows = new Map<string, Set<string>>();

function getCharacterQueue(): WorkflowQueue {
    return characterQueue;
}

function isCharacterWorkflowActive(conversationId: number, character: string): boolean {
    const conversationWorkflows = activeCharacterWorkflows.get(conversationId.toString());
    return conversationWorkflows?.has(character) || false;
}

function markCharacterWorkflowActive(conversationId: number, character: string): void {
    const conversationKey = conversationId.toString();
    if (!activeCharacterWorkflows.has(conversationKey)) {
        activeCharacterWorkflows.set(conversationKey, new Set());
    }
    activeCharacterWorkflows.get(conversationKey)!.add(character);
}

function markCharacterWorkflowComplete(conversationId: number, character: string): void {
    const conversationKey = conversationId.toString();
    const conversationWorkflows = activeCharacterWorkflows.get(conversationKey);
    if (conversationWorkflows) {
        conversationWorkflows.delete(character);
        if (conversationWorkflows.size === 0) {
            activeCharacterWorkflows.delete(conversationKey);
        }
    }
}

// Legacy ForaChat class for backward compatibility
// New code should use ChatService instead
export class ForaChat {
    // Static LLM service instance for dependency injection
    private static llmService: LLMService | null = null;

    // Method to set LLM service for testing
    static setLLMService(service: LLMService): void {
        ForaChat.llmService = service;
    }

    // Helper method to get the LLM service (injected or default)
    private static getLLMService(): LLMService {
        return ForaChat.llmService || new GeminiLLMService();
    }

    static async createConversation(): Promise<Conversation> {
        return ConversationService.createConversation();
    }

    static async addMessage(character: string, text: string, conversation_id: number): Promise<Message> {
        return ConversationService.addMessage(character, text, conversation_id);
    }

    static async getSystemPrompt(): Promise<string> {
        return PromptService.getSystemPrompt();
    }

    @DBOS.transaction()
    static async getMessageCount(conversationId: number): Promise<number> {
        const result = await DBOS.knexClient('forachat.messages')
            .where('conversation_id', conversationId)
            .count('id as count')
            .first() as { count: string } | undefined;
        return result ? Number(result.count) : 0;
    }

    @DBOS.transaction()
    static async updateConversationMetadata(
        conversationId: number,
        theme?: string,
        skills?: string[]
    ): Promise<void> {
        const updateData: any = {};
        if (theme !== undefined) updateData.theme = theme;
        if (skills !== undefined) updateData.skills = JSON.stringify(skills);

        if (Object.keys(updateData).length > 0) {
            await DBOS.knexClient('forachat.conversations')
                .where('id', conversationId)
                .update(updateData);
        }
    }

    static async getDelayedThoughts(conversationId: number, lastMessageId?: number): Promise<DelayedMessage[]> {
        return ConversationService.getDelayedThoughts(conversationId, lastMessageId);
    }

    static async getConversation(conversationId: number): Promise<Conversation | null> {
        return ConversationService.getConversation(conversationId);
    }

    @DBOS.workflow()
    static async getConversationMessages(conversationId: number): Promise<Message[]> {
        return ConversationService.getConversationMessages(conversationId);
    }

    @DBOS.workflow()
    static async updateConversationEngagement(conversationId: number): Promise<{ engagementLevel: number; shouldTimeout: boolean; delayMultiplier: number }> {
        return ConversationDecayService.updateConversationEngagement(conversationId);
    }

    @DBOS.workflow()
    static async getLastUserActivity(conversationId: number): Promise<Date | null> {
        return ConversationService.getLastUserActivity(conversationId);
    }



    @DBOS.workflow()
    static async chatWorkflow(userMessage: string, conversationId?: number): Promise<any> {
        let conversation;
        if (conversationId) {
            // Use existing conversation
            conversation = { id: conversationId };
        } else {
            // Create new conversation
            conversation = await ForaChat.createConversation();
        }

        await ForaChat.addMessage("user", userMessage, conversation.id);

        // Get message count for logging context
        const messageCount = await ForaChat.getMessageCount(conversation.id);

        const systemPrompt = await ForaChat.getSystemPrompt();
        const llmService = ForaChat.getLLMService();

        // Include recent conversation messages for context if this is a continuing conversation
        let finalPrompt = userMessage;
        if (conversationId) {
            // Get recent messages for context (excluding the just-added user message)
            const recentMessages = await ForaChat.getConversationMessages(conversationId);
            const contextMessages = recentMessages
                .slice(-11, -1) // Get last 10 messages before the current user message
                .map(msg => `${msg.character}: ${msg.text}`)
                .join('\n');

            if (contextMessages.length > 0) {
                finalPrompt = `Recent conversation:\n${contextMessages}\n\nCurrent user message: ${userMessage}`;
            }
        }

        const llmResponse = await llmService.generate(systemPrompt, finalPrompt);

        // Log parsed LLM response with context
        DBOS.logger.info(`=== LLM RESPONSE ===`);
        DBOS.logger.info(`Conversation: ${conversation.id}, Message Count: ${messageCount}, Responder: default agent`);
        DBOS.logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply)) {
            DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or invalid reply array");
        }

        // Handle case where LLM determines request is not related to interpersonal skills
        if (llmResponse.reply.length === 0) {
            DBOS.logger.info(`LLM determined request is not related to interpersonal skills. Theme: ${llmResponse.theme}`);

            // Don't show warning for general greetings
            if (llmResponse.theme !== "general greeting") {
                // Create a helpful response explaining this
                const helpfulResponse = {
                    character: "Fora",
                    text: "Hey! 👋 I noticed your message might not be about interpersonal workplace skills. I'm here to help with things like communication, teamwork, conflict resolution, and other people skills at work. Could you rephrase your question to focus on the interpersonal aspect? 😊",
                    delay: 1000
                };

                llmResponse.reply = [helpfulResponse];
                llmResponse.theme = llmResponse.theme || "Request clarification needed";
                llmResponse.skills = llmResponse.skills || [];
            } else {
                // For general greetings, keep empty reply but preserve theme
                llmResponse.skills = llmResponse.skills || [];
            }
        }

        // Store all response messages with duplicate checking
        for (const responseMessage of llmResponse.reply) {
            const messageExists = await ConversationService.checkMessageExists(
                responseMessage.character,
                responseMessage.text,
                conversation.id
            );

            if (!messageExists) {
                await ForaChat.addMessage(responseMessage.character, responseMessage.text, conversation.id);
                DBOS.logger.info(`Added response message from ${responseMessage.character}: ${responseMessage.text.substring(0, 50)}...`);
            } else {
                DBOS.logger.info(`Skipped duplicate response from ${responseMessage.character}: ${responseMessage.text.substring(0, 50)}...`);
            }
        }

        // Store theme and skills if provided in the LLM response
        if (llmResponse.theme || llmResponse.skills) {
            await ForaChat.updateConversationMetadata(
                conversation.id,
                llmResponse.theme,
                llmResponse.skills
            );
        }

        // Queue background thoughts for all characters to continue the conversation
        const allCharacters = ['Fora', 'Jan', 'Lou'];
        const conversationContext = `User: ${userMessage}\n` +
            llmResponse.reply.map((msg: DelayedMessage) => `${msg.character}: ${msg.text}`).join('\n');

        // Queue delayed thoughts for all characters to keep the conversation going
        for (const character of allCharacters) {
            // For general greetings, use shorter delays (5-15 seconds)
            // For other themes, use longer delays (30-60 seconds)
            const isGeneralGreeting = llmResponse.theme === "general greeting";
            const baseDelay = isGeneralGreeting ? 5000 : 30000;
            const randomDelay = isGeneralGreeting ? Math.random() * 10000 : Math.random() * 30000;
            const initialDelay = baseDelay + randomDelay;

            // Start the delayed character thought workflow directly
            // The workflow will handle its own timing and decay logic
            await DBOS.startWorkflow(ForaChat, {
                queueName: getCharacterQueue().name
            }).delayedCharacterThoughtWorkflow(conversation.id, conversationContext, character, initialDelay);
        }

        return { ...llmResponse, conversationId: conversation.id };
    }

    @DBOS.workflow()
    static async interruptedChatWorkflow(
        userMessage: string,
        previousMessages: Array<{character: string, text: string}>,
        conversationId?: number
    ): Promise<any> {
        let conversation;
        if (conversationId) {
            conversation = { id: conversationId };
        } else {
            conversation = await ForaChat.createConversation();
        }

        // Build context from previous messages and new user input
        const contextMessages = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');
        const fullPrompt = `Previous conversation:\n${contextMessages}\n\nUser (interrupting): ${userMessage}`;

        await ForaChat.addMessage("user", userMessage, conversation.id);

        // Get message count for logging context
        const messageCount = await ForaChat.getMessageCount(conversation.id);

        const systemPrompt = await ForaChat.getSystemPrompt();

        const llmService = ForaChat.getLLMService();
        const llmResponse = await llmService.generate(systemPrompt, fullPrompt);

        // Log parsed LLM response with context
        DBOS.logger.info(`=== LLM RESPONSE (INTERRUPTED CHAT) ===`);
        DBOS.logger.info(`Conversation ID: ${conversation.id}, Message Count: ${messageCount}, Responder: default agent (interrupted)`);
        DBOS.logger.info(`Previous Messages Count: ${previousMessages.length}`);
        DBOS.logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply)) {
            DBOS.logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or invalid reply array");
        }

        // Handle case where LLM determines request is not related to interpersonal skills
        if (llmResponse.reply.length === 0) {
            DBOS.logger.info(`LLM determined interrupted request is not related to interpersonal skills. Theme: ${llmResponse.theme}`);

            // Don't show warning for general greetings
            if (llmResponse.theme !== "general greeting") {
                // Create a helpful response explaining this
                const helpfulResponse = {
                    character: "Fora",
                    text: "Hey! 👋 I noticed your message might not be about interpersonal workplace skills. I'm here to help with things like communication, teamwork, conflict resolution, and other people skills at work. Could you rephrase your question to focus on the interpersonal aspect? 😊",
                    delay: 1000
                };

                llmResponse.reply = [helpfulResponse];
                llmResponse.theme = llmResponse.theme || "Request clarification needed";
                llmResponse.skills = llmResponse.skills || [];
            } else {
                // For general greetings, keep empty reply but preserve theme
                llmResponse.skills = llmResponse.skills || [];
            }
        }

        // Store all response messages, but check for duplicates in the queue first
        for (const responseMessage of llmResponse.reply) {
            // Check if this message is already being processed in the queue to prevent duplicates
            const similarityCheck = await MessageQueueService.checkSimilarity(
                conversation.id,
                responseMessage.character,
                responseMessage.text
            );

            if (!similarityCheck.isDuplicate && !similarityCheck.isSimilar) {
                await ForaChat.addMessage(responseMessage.character, responseMessage.text, conversation.id);
                DBOS.logger.info(`Added interrupted response message from ${responseMessage.character}: ${responseMessage.text.substring(0, 50)}...`);
            } else {
                DBOS.logger.info(`Skipped duplicate interrupted response from ${responseMessage.character} (already in queue): ${responseMessage.text.substring(0, 50)}...`);
            }
        }

        // Store theme and skills if provided in the LLM response
        if (llmResponse.theme || llmResponse.skills) {
            await ForaChat.updateConversationMetadata(
                conversation.id,
                llmResponse.theme,
                llmResponse.skills
            );
        }

        // Queue background thoughts for all characters to continue the conversation
        const allCharacters = ['Fora', 'Jan', 'Lou'];

        // Queue delayed thoughts for all characters to keep the conversation going
        for (const character of allCharacters) {
            // For general greetings, use shorter delays (5-15 seconds)
            // For other themes, use longer delays (30-60 seconds)
            const isGeneralGreeting = llmResponse.theme === "general greeting";
            const baseDelay = isGeneralGreeting ? 5000 : 30000;
            const randomDelay = isGeneralGreeting ? Math.random() * 10000 : Math.random() * 30000;
            const initialDelay = baseDelay + randomDelay;

            // Start the delayed character thought workflow directly
            // The workflow will handle its own timing and decay logic
            await DBOS.startWorkflow(ForaChat, {
                queueName: getCharacterQueue().name
            }).delayedCharacterThoughtWorkflow(conversation.id, fullPrompt, character, initialDelay);
        }

        return { ...llmResponse, conversationId: conversation.id };
    }

    @DBOS.transaction()
    static async getConversationRelevanceContext(conversationId: number): Promise<{
        conversation: Conversation | null;
        recentMessages: Message[];
        messageCount: number;
    }> {
        // Get conversation metadata directly (avoid nested transaction)
        const conversation = await DBOS.knexClient<Conversation>('forachat.conversations')
            .where('id', conversationId)
            .first();

        // Parse skills JSON if it's stored as string (same logic as getConversation)
        if (conversation && conversation.skills && typeof conversation.skills === 'string') {
            conversation.skills = JSON.parse(conversation.skills as string);
        }

        // Build context from recent messages
        const recentMessages = await DBOS.knexClient<Message>('forachat.messages')
            .where('conversation_id', conversationId)
            .orderBy('id', 'desc')
            .limit(5);

        // Get message count directly (avoid nested transaction)
        const messageCountResult = await DBOS.knexClient('forachat.messages')
            .where('conversation_id', conversationId)
            .count('id as count')
            .first() as { count: string } | undefined;

        const messageCount = messageCountResult ? Number(messageCountResult.count) : 0;

        return {
            conversation: conversation || null,
            recentMessages: recentMessages.reverse(),
            messageCount
        };
    }

    @DBOS.workflow()
    static async determineConversationRelevance(
        userMessage: string,
        conversationId: number
    ): Promise<boolean> {
        // Get conversation context through transaction
        const { conversation, recentMessages, messageCount } = await ForaChat.getConversationRelevanceContext(conversationId);

        const context = recentMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Log conversation relevance analysis context
        DBOS.logger.info(`=== CONVERSATION RELEVANCE ANALYSIS ===`);
        DBOS.logger.info(`Conversation ID: ${conversationId}`);
        DBOS.logger.info(`Message Count in Context: ${messageCount}`);
        DBOS.logger.info(`User Message: "${userMessage}"`);
        DBOS.logger.info(`Recent Context (${recentMessages.length} messages): ${context || 'No previous messages'}`);
        DBOS.logger.info(`Theme: ${conversation?.theme || 'Not set'}`);
        DBOS.logger.info(`Skills: ${conversation?.skills ? JSON.stringify(conversation.skills) : 'Not set'}`);

        // Get system prompt
        const systemPrompt = await ForaChat.getSystemPrompt();

        // Ask LLM to determine if the message is related to the conversation with detailed reasoning
        const llmService = ForaChat.getLLMService();
        const llmResponse = await llmService.generate(
            systemPrompt,
            `Analyze if this user message is related to the ongoing conversation and provide detailed reasoning.

Conversation context:
${context || 'No previous messages in this conversation'}

${conversation?.theme ? `Conversation theme: ${conversation.theme}` : ''}
${conversation?.skills ? `Conversation skills: ${Array.isArray(conversation.skills) ? conversation.skills.join(', ') : conversation.skills}` : ''}

User message: ${userMessage}

Respond with a JSON object containing:
- "isRelated": boolean (true if related to the conversation, false if it's a new topic)
- "reasoning": string (detailed explanation of why you think it is or isn't related)
- "confidence": number (0-100, how confident you are in this assessment)
- "keyFactors": array of strings (key factors that influenced your decision)

Consider factors like:
- Topic continuity
- Reference to previous messages or characters
- Contextual relevance
- Natural conversation flow
- Whether it's a complete topic change`
        );

        // Log the LLM's detailed analysis
        if (llmResponse && typeof llmResponse === 'object') {
            DBOS.logger.info(`=== LLM CONVERSATION RELEVANCE DECISION ===`);
            // Cast to any to access custom properties that aren't in ChatResponse
            const response = llmResponse as any;
            DBOS.logger.info(`Is Related: ${response.isRelated}`);
            DBOS.logger.info(`Confidence: ${response.confidence || 'Not provided'}%`);
            DBOS.logger.info(`Reasoning: ${response.reasoning || 'No reasoning provided'}`);
            DBOS.logger.info(`Key Factors: ${response.keyFactors ? JSON.stringify(response.keyFactors) : 'Not provided'}`);

            // Extract the determination
            if ('isRelated' in response) {
                const isRelated = !!response.isRelated;
                DBOS.logger.info(`Final Decision: ${isRelated ? 'CONTINUING existing conversation' : 'STARTING new conversation'}`);
                return isRelated;
            }
        }

        // Log if we couldn't parse the response
        DBOS.logger.warn(`Could not parse LLM relevance response: ${JSON.stringify(llmResponse)}`);
        DBOS.logger.info(`Final Decision: CONTINUING existing conversation (default fallback)`);

        // Default to true if we can't determine
        return true;
    }

    @DBOS.workflow()
    static async delayedCharacterThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string,
        initialDelay: number
    ): Promise<any> {
        // Apply initial delay
        await new Promise(resolve => setTimeout(resolve, initialDelay));

        try {
            // Check conversation engagement and apply decay
            const decayInfo = await ForaChat.updateConversationEngagement(conversationId);

            if (decayInfo.shouldTimeout) {
                DBOS.logger.info(`Conversation ${conversationId} timed out due to low engagement (${Math.round(decayInfo.engagementLevel * 100)}%). Skipping ${character} thought.`);
                markCharacterWorkflowComplete(conversationId, character);
                return null;
            }

            const adjustedDelay = ConversationDecayService.applyDecayToDelay(initialDelay, decayInfo.delayMultiplier);

            if (adjustedDelay === Infinity) {
                DBOS.logger.info(`Conversation ${conversationId} delay timeout. Skipping ${character} thought.`);
                markCharacterWorkflowComplete(conversationId, character);
                return null;
            }

            const lastActivity = await ForaChat.getLastUserActivity(conversationId);
            const timeSinceActivity = lastActivity ? Date.now() - lastActivity.getTime() : 0;
            const decayStatus = ConversationDecayService.getDecayStatus(
                timeSinceActivity,
                decayInfo.engagementLevel,
                decayInfo.delayMultiplier
            );

            DBOS.logger.info(`${character} thought scheduled with decay: ${decayStatus}`);

            // Apply additional delay if needed
            if (adjustedDelay > initialDelay) {
                await new Promise(resolve => setTimeout(resolve, adjustedDelay - initialDelay));
            }

            // Now execute the actual character thought workflow
            return await ForaChat.characterThoughtWorkflow(conversationId, context, character);
        } catch (error) {
            DBOS.logger.error(`Error in delayed character thought workflow for ${character}: ${(error as Error).message}`);
            markCharacterWorkflowComplete(conversationId, character);
            return null;
        }
    }

    @DBOS.workflow()
    static async characterThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string
    ): Promise<any> {
        // Check if this character already has an active workflow to prevent duplicates
        if (isCharacterWorkflowActive(conversationId, character)) {
            DBOS.logger.info(`Skipping ${character} thought - workflow already active for this character`);
            return null;
        }

        // Mark this workflow as active
        markCharacterWorkflowActive(conversationId, character);

        try {
            // Check for recent messages from this character to prevent immediate self-replies
            const recentMessages = await ConversationService.getDelayedThoughts(conversationId);
            if (recentMessages.length > 0) {
                // Check for recent messages from this character (within last 5 minutes)
                const fiveMinutesAgo = Date.now() - 300000;
                const recentCharacterMessages = recentMessages
                    .filter(msg => msg.character === character)
                    .filter(msg => new Date(msg.created_at!).getTime() > fiveMinutesAgo);

                if (recentCharacterMessages.length > 0) {
                    const lastMessage = recentCharacterMessages[recentCharacterMessages.length - 1];
                    const timeSinceLastMessage = Date.now() - new Date(lastMessage.created_at!).getTime();
                    const MIN_COOLDOWN = 30000; // 30 seconds minimum between same character messages

                    if (timeSinceLastMessage < MIN_COOLDOWN) {
                        DBOS.logger.info(`Skipping ${character} thought - too soon after their last message (${timeSinceLastMessage}ms < ${MIN_COOLDOWN}ms)`);
                        markCharacterWorkflowComplete(conversationId, character);
                        return null;
                    }
                }
            }

            // Get pending messages to provide context for better responses
            const pendingMessages = await MessageQueueService.getPendingMessages(conversationId);
            const pendingContext = pendingMessages.length > 0
                ? `\n\nPending messages that will appear soon:\n${pendingMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n')}`
                : '';

            // Add a delay before generating the thought (simulate thinking time)
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 8000));

            // Use character-specific system prompt
            const characterPromptName = `${character.toLowerCase()}_system`;
            const systemPrompt = await PromptService.getSystemPrompt(characterPromptName);

            const thoughtPrompt = `Based on this conversation context, generate a brief follow-up thought or comment that adds value to the discussion.

Consider any pending messages that will appear soon to avoid redundancy or similar content.

Context:
${context}${pendingContext}

Respond with a natural, contextual message that continues the conversation. You must respond with JSON in exactly this format:

{
  "reply": [
    {
      "character": "${character}",
      "text": "your_message_here",
      "delay": 2000
    }
  ],
  "skills": [],
  "theme": "follow-up thought"
}`;

            const llmService = ForaChat.getLLMService();
            const llmResponse = await llmService.generate(systemPrompt, thoughtPrompt);

            // Log parsed LLM response with character context
            DBOS.logger.info(`=== LLM RESPONSE (CHARACTER THOUGHT) ===`);
            DBOS.logger.info(`Conversation: ${conversationId}, Responder: ${character} character`);
            DBOS.logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

            if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
                const thought = llmResponse.reply[0];

                // Try to enqueue the message (this will handle duplicate/similarity checking)
                const queuedMessage = await MessageQueueService.enqueueMessage({
                    conversation_id: conversationId,
                    character: thought.character,
                    text: thought.text,
                    delay_ms: thought.delay || 2000
                });

                if (queuedMessage) {
                    DBOS.logger.info(`Enqueued ${character} thought: ${thought.text.substring(0, 50)}...`);
                    return {
                        character,
                        text: thought.text,
                        conversationId
                    };
                } else {
                    DBOS.logger.info(`${character} thought was filtered out (duplicate/similar)`);
                    return null;
                }
            }

            return null;
        } finally {
            // Always mark the workflow as complete
            markCharacterWorkflowComplete(conversationId, character);
        }
    }

    @DBOS.workflow()
    static async extendedWorkflowThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string
    ): Promise<any> {
        // Check if this character already has an active workflow to prevent duplicates
        if (isCharacterWorkflowActive(conversationId, character)) {
            DBOS.logger.info(`Skipping ${character} extended thought - workflow already active for this character`);
            return null;
        }

        // Mark this workflow as active
        markCharacterWorkflowActive(conversationId, character);

        try {
            // Check for recent messages from this character to prevent immediate self-replies
            const recentMessages = await ConversationService.getDelayedThoughts(conversationId);
            if (recentMessages.length > 0) {
                // Check for recent messages from this character (within last 5 minutes)
                const fiveMinutesAgo = Date.now() - 300000;
                const recentCharacterMessages = recentMessages
                    .filter(msg => msg.character === character)
                    .filter(msg => new Date(msg.created_at!).getTime() > fiveMinutesAgo);

                if (recentCharacterMessages.length > 0) {
                    const lastMessage = recentCharacterMessages[recentCharacterMessages.length - 1];
                    const timeSinceLastMessage = Date.now() - new Date(lastMessage.created_at!).getTime();
                    const MIN_COOLDOWN = 30000; // 30 seconds minimum between same character messages

                    if (timeSinceLastMessage < MIN_COOLDOWN) {
                        DBOS.logger.info(`Skipping ${character} extended thought - too soon after their last message (${timeSinceLastMessage}ms < ${MIN_COOLDOWN}ms)`);
                        markCharacterWorkflowComplete(conversationId, character);
                        return null;
                    }
                }
            }

            // Add a delay before generating the thought (simulate thinking time)
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 8000));

            // Use character-specific system prompt
            const characterPromptName = `${character.toLowerCase()}_system`;
            const systemPrompt = await PromptService.getSystemPrompt(characterPromptName);

            const thoughtPrompt = `Based on this conversation context, generate a brief follow-up thought or comment that adds value to the discussion.

This is for an extended workflow message that will be sent immediately, not queued.

Context:
${context}

Respond with a natural, contextual message that continues the conversation. You must respond with JSON in exactly this format:

{
  "reply": [
    {
      "character": "${character}",
      "text": "your_message_here",
      "delay": 2000
    }
  ],
  "skills": [],
  "theme": "extended workflow thought"
}`;

            const llmService = ForaChat.getLLMService();
            const llmResponse = await llmService.generate(systemPrompt, thoughtPrompt);

            // Log parsed LLM response with character context
            DBOS.logger.info(`=== LLM RESPONSE (EXTENDED WORKFLOW THOUGHT) ===`);
            DBOS.logger.info(`Conversation: ${conversationId}, Responder: ${character} character`);
            DBOS.logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

            if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
                const thought = llmResponse.reply[0];

                // For extended workflow, we DON'T enqueue the message - it will be sent immediately
                // This prevents duplication with delayed_thought messages
                DBOS.logger.info(`Generated ${character} extended workflow thought: ${thought.text.substring(0, 50)}...`);
                return {
                    character,
                    text: thought.text,
                    conversationId
                };
            }

            return null;
        } finally {
            // Always mark the workflow as complete
            markCharacterWorkflowComplete(conversationId, character);
        }
    }
}
