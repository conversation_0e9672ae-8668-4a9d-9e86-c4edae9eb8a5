import express, { Request, Response } from 'express';
import cookieParser from 'cookie-parser';
import { DBOS } from '@dbos-inc/dbos-sdk';
import { ChatService } from '../core/ChatService';
import { ForaChat } from '../operations';
import { MessageInterface, ChatResponse, SessionCreateRequest } from '../models/types';
import { v4 as uuidv4 } from 'uuid';

export class WebInterface implements MessageInterface {
  private app: express.Application;
  private chatService: ChatService;

  constructor(chatService: ChatService) {
    this.chatService = chatService;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    this.app.use(express.json());
    this.app.use(cookieParser());

    // Add CORS if needed
    this.app.use((req, res, next) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Cookie');
      res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
      res.header('Access-Control-Allow-Credentials', 'true');
      next();
    });

    // Session middleware - ensure user has a session
    this.app.use(this.ensureSession.bind(this));

    // Serve static files from the React build directory only
    this.app.use(express.static('public/dist'));
  }

  private async ensureSession(req: Request, res: Response, next: express.NextFunction): Promise<void> {
    try {
      // Skip session handling for health check and static files
      if (req.path === '/health' || req.path.startsWith('/dist/') || req.path.endsWith('.js') || req.path.endsWith('.css') || req.path.endsWith('.svg')) {
        next();
        return;
      }

      let sessionId = req.cookies?.forachat_session;
      let session = null;

      if (sessionId) {
        // Try to get existing session
        const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
        session = await handle.getResult();
      }

      if (!session) {
        // Create new session
        sessionId = uuidv4();
        const userIdentifier = req.ip || 'unknown';

        const sessionRequest: SessionCreateRequest = {
          userIdentifier: `web_${userIdentifier}`,
          channel: 'web',
          metadata: {
            userAgent: req.headers['user-agent'],
            ip: req.ip
          }
        };

        const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
        session = await handle.getResult();
        sessionId = session.id;

        // Set cookie
        res.cookie('forachat_session', sessionId, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
        });
      } else {
        // Update session activity
        await DBOS.startWorkflow(ForaChat).updateSessionActivity(sessionId);
      }

      // Add session to request for use in handlers
      (req as any).session = session;
      next();
    } catch (error) {
      DBOS.logger.error(`Error in session middleware: ${(error as Error).message}`);
      next(error);
    }
  }

  private setupRoutes(): void {
    // Serve the React build at root
    this.app.get('/', (req, res) => {
      res.sendFile('index.html', { root: 'public/dist' });
    });

    // Main chat endpoint
    this.app.post('/chat', this.handleChatRequest.bind(this));

    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({ status: 'healthy', timestamp: new Date().toISOString() });
    });

    // Get conversation history
    this.app.get('/conversation/:id', this.getConversationHistory.bind(this));

    // Continue existing conversation
    this.app.post('/conversation/:id/message', this.continueConversation.bind(this));

    // Session management endpoints
    this.app.get('/session', this.getSessionInfo.bind(this));
    this.app.get('/session/conversations', this.getSessionConversations.bind(this));

    // API endpoints for REPL and other clients
    this.app.post('/api/session', this.createSessionAPI.bind(this));
    this.app.get('/api/session/:id', this.getSessionInfoAPI.bind(this));
    this.app.delete('/api/session/:id', this.deleteSessionAPI.bind(this));
    this.app.put('/api/session/:id/extend', this.extendSessionAPI.bind(this));
    this.app.get('/api/session/:id/conversation', this.getSessionConversationAPI.bind(this));
    this.app.get('/api/user/:identifier/sessions', this.getUserSessionsAPI.bind(this));
    this.app.post('/api/sessions/cleanup', this.cleanupSessionsAPI.bind(this));
    this.app.get('/api/sessions/stats', this.getCleanupStatsAPI.bind(this));
    this.app.post('/api/sessions/manual-cleanup', this.manualCleanupAPI.bind(this));
  }

  private async handleChatRequest(req: Request, res: Response): Promise<void> {
    try {
      const { text } = req.body;
      const session = (req as any).session;

      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        res.status(400).json({
          error: 'Request body must include a non-empty "text" field'
        });
        return;
      }

      let result;
      if (session?.conversation_id) {
        // Continue existing conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text.trim(), session.conversation_id);
        result = await handle.getResult();
      } else {
        // Start new conversation
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text.trim());
        result = await handle.getResult();

        // Update session with new conversation ID
        if (result.conversationId) {
          await DBOS.startWorkflow(ForaChat).updateSessionConversation(session.id, result.conversationId);
        }
      }

      res.json(result);

    } catch (error) {
      DBOS.logger.error(`Error in /chat handler: ${(error as Error).message}`);
      const errorMessage = (error as Error).message;
      res.status(500).json({
        error: "An internal server error occurred.",
        details: errorMessage
      });
    }
  }

  private async continueConversation(req: Request, res: Response): Promise<void> {
    try {
      const conversationId = parseInt(req.params.id);
      const { text } = req.body;

      if (isNaN(conversationId)) {
        res.status(400).json({ error: 'Invalid conversation ID' });
        return;
      }

      if (!text || typeof text !== 'string' || text.trim().length === 0) {
        res.status(400).json({ 
          error: 'Request body must include a non-empty "text" field' 
        });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text.trim(), conversationId);
      const result = await handle.getResult();
      res.json(result);
      
    } catch (error) {
      DBOS.logger.error(`Error continuing conversation: ${(error as Error).message}`);
      const errorMessage = (error as Error).message;
      
      if (errorMessage.includes('not found')) {
        res.status(404).json({ error: errorMessage });
      } else {
        res.status(500).json({ 
          error: "An internal server error occurred.", 
          details: errorMessage 
        });
      }
    }
  }

  private async getConversationHistory(req: Request, res: Response): Promise<void> {
    try {
      const conversationId = parseInt(req.params.id);

      if (isNaN(conversationId)) {
        res.status(400).json({ error: 'Invalid conversation ID' });
        return;
      }

      // Get conversation messages using DBOS workflow
      const handle = await DBOS.startWorkflow(ForaChat).getConversationMessages(conversationId);
      const messages = await handle.getResult();

      res.json({ conversationId, messages });

    } catch (error) {
      DBOS.logger.error(`Error getting conversation history: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionInfo(req: Request, res: Response): Promise<void> {
    try {
      const session = (req as any).session;

      if (!session) {
        res.status(404).json({ error: 'No session found' });
        return;
      }

      res.json({
        sessionId: session.id,
        conversationId: session.conversation_id,
        channel: session.channel,
        createdAt: session.created_at,
        lastActivity: session.last_activity
      });
    } catch (error) {
      DBOS.logger.error(`Error getting session info: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionConversations(req: Request, res: Response): Promise<void> {
    try {
      const session = (req as any).session;

      if (!session) {
        res.status(404).json({ error: 'No session found' });
        return;
      }

      if (!session.conversation_id) {
        res.json({ conversations: [] });
        return;
      }

      // Get conversation messages
      const handle = await DBOS.startWorkflow(ForaChat).getConversationMessages(session.conversation_id);
      const messages = await handle.getResult();

      res.json({
        conversationId: session.conversation_id,
        messages
      });
    } catch (error) {
      DBOS.logger.error(`Error getting session conversations: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async createSessionAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionRequest: SessionCreateRequest = req.body;

      if (!sessionRequest.userIdentifier || !sessionRequest.channel) {
        res.status(400).json({
          error: 'userIdentifier and channel are required'
        });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).createSession(sessionRequest);
      const session = await handle.getResult();

      res.json({
        sessionId: session.id,
        conversationId: session.conversation_id,
        channel: session.channel,
        createdAt: session.created_at
      });
    } catch (error) {
      DBOS.logger.error(`Error creating session: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionInfoAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).getSession(sessionId);
      const session = await handle.getResult();

      if (!session) {
        res.status(404).json({ error: 'Session not found' });
        return;
      }

      res.json({
        sessionId: session.id,
        conversationId: session.conversation_id,
        channel: session.channel,
        createdAt: session.created_at,
        lastActivity: session.last_activity
      });
    } catch (error) {
      DBOS.logger.error(`Error getting session info: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async deleteSessionAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      await DBOS.startWorkflow(ForaChat).deleteSession(sessionId);
      res.json({ message: 'Session deleted successfully' });
    } catch (error) {
      DBOS.logger.error(`Error deleting session: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async extendSessionAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;
      const { hours = 24 } = req.body;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      await DBOS.startWorkflow(ForaChat).extendSession(sessionId, hours);
      res.json({ message: `Session extended by ${hours} hours` });
    } catch (error) {
      DBOS.logger.error(`Error extending session: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getSessionConversationAPI(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.params.id;

      if (!sessionId) {
        res.status(400).json({ error: 'Session ID is required' });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).getConversationBySession(sessionId);
      const conversation = await handle.getResult();

      if (!conversation) {
        res.status(404).json({ error: 'No conversation found for this session' });
        return;
      }

      const messagesHandle = await DBOS.startWorkflow(ForaChat).getConversationMessages(conversation.id);
      const messages = await messagesHandle.getResult();

      res.json({
        sessionId,
        conversation,
        messages
      });
    } catch (error) {
      DBOS.logger.error(`Error getting session conversation: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getUserSessionsAPI(req: Request, res: Response): Promise<void> {
    try {
      const userIdentifier = req.params.identifier;

      if (!userIdentifier) {
        res.status(400).json({ error: 'User identifier is required' });
        return;
      }

      const handle = await DBOS.startWorkflow(ForaChat).getActiveSessionsForUser(userIdentifier);
      const sessions = await handle.getResult();

      res.json({ userIdentifier, sessions });
    } catch (error) {
      DBOS.logger.error(`Error getting user sessions: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async cleanupSessionsAPI(req: Request, res: Response): Promise<void> {
    try {
      const handle = await DBOS.startWorkflow(ForaChat).cleanupExpiredSessions();
      const deletedCount = await handle.getResult();

      res.json({
        message: `Cleaned up ${deletedCount} expired sessions`,
        deletedCount
      });
    } catch (error) {
      DBOS.logger.error(`Error cleaning up sessions: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async getCleanupStatsAPI(req: Request, res: Response): Promise<void> {
    try {
      const handle = await DBOS.startWorkflow(ForaChat).getCleanupStats();
      const stats = await handle.getResult();

      res.json(stats);
    } catch (error) {
      DBOS.logger.error(`Error getting cleanup stats: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  private async manualCleanupAPI(req: Request, res: Response): Promise<void> {
    try {
      const options = req.body || {};

      const handle = await DBOS.startWorkflow(ForaChat).manualSessionCleanup(options);
      const result = await handle.getResult();

      res.json({
        message: 'Manual cleanup completed',
        ...result
      });
    } catch (error) {
      DBOS.logger.error(`Error performing manual cleanup: ${(error as Error).message}`);
      res.status(500).json({
        error: "An internal server error occurred.",
        details: (error as Error).message
      });
    }
  }

  // MessageInterface implementation
  async sendMessage(message: string): Promise<void> {
    // For web interface, this would typically be handled by the client
    console.log(`Web interface would send: ${message}`);
  }

  async receiveMessage(): Promise<string> {
    // For web interface, messages come through HTTP requests
    throw new Error('receiveMessage not applicable for web interface');
  }

  formatResponse(response: ChatResponse): string {
    return JSON.stringify(response);
  }

  getApp(): express.Application {
    return this.app;
  }
}
