import readline from 'readline';
import http from 'http';
import { MessageInterface, ChatResponse } from '../models/types';

export class REPLInterface implements MessageInterface {
  private rl: readline.Interface;
  private serverUrl: string;
  private serverPort: number;
  private currentConversationId: number | null = null;
  private lastMessageId: number = 0;
  private pollingInterval: NodeJS.Timeout | null = null;

  constructor(serverUrl: string = 'localhost', serverPort: number = 3000) {
    this.serverUrl = serverUrl;
    this.serverPort = serverPort;
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: '> '
    });
  }

  async start(): Promise<void> {
    console.log('🚀 ForaChat REPL started!');
    console.log('Type your workplace questions and get advice from <PERSON><PERSON>, <PERSON>, and <PERSON>.');
    console.log('Type "exit" or "quit" to end the session.\n');
    
    this.rl.prompt();
    
    this.rl.on('line', async (line) => {
      const input = line.trim();
      
      if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
        this.rl.close();
        return;
      }
      
      if (input.length === 0) {
        this.rl.prompt();
        return;
      }

      try {
        await this.sendMessage(input);
      } catch (error) {
        console.error(`Error: ${(error as Error).message}`);
      }
      
      this.rl.prompt();
    });

    this.rl.on('close', () => {
      // Clean up polling
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
      }
      console.log('\n👋 Thanks for using ForaChat! Have a great day!');
      process.exit(0);
    });
  }

  async sendMessage(message: string): Promise<void> {
    const postData = JSON.stringify({ text: message });

    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: '/chat',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            this.displayResponse(response);
            resolve();
          } catch (error) {
            console.error('Failed to parse response:', error);
            console.error('Raw response:', data);
            reject(error);
          }
        });
      });

      req.on('error', (e) => {
        console.error(`Connection error: ${e.message}`);
        console.error('Make sure the ForaChat server is running on port 3000');
        reject(e);
      });

      req.write(postData);
      req.end();
    });
  }

  async receiveMessage(): Promise<string> {
    return new Promise((resolve) => {
      this.rl.question('> ', (answer) => {
        resolve(answer.trim());
      });
    });
  }

  formatResponse(response: ChatResponse): string {
    let formatted = `\n--- ${response.theme || 'Chat Response'} ---\n`;
    
    response.reply.forEach((message) => {
      formatted += `${message.character}: ${message.text}\n`;
    });
    
    if (response.skills && response.skills.length > 0) {
      formatted += `\nSkills: ${response.skills.join(', ')}\n`;
    }
    
    return formatted;
  }

  private displayResponse(response: any): void {
    // Handle error responses
    if (response.error) {
      console.log(`❌ Error: ${response.error}`);
      if (response.details) {
        console.log(`Details: ${response.details}`);
      }
      return;
    }

    // Handle successful responses with reply array
    if (response.reply && Array.isArray(response.reply)) {
      console.log(this.formatResponse(response));

      // Store conversation ID and start polling for delayed thoughts
      if (response.conversationId) {
        this.currentConversationId = response.conversationId;
        // Set initial lastMessageId to avoid re-showing initial messages
        this.setInitialLastMessageId();
        this.startPollingForDelayedThoughts();
      }
    } else {
      console.log('Unexpected response format:', JSON.stringify(response, null, 2));
    }
  }

  private async setInitialLastMessageId(): Promise<void> {
    if (!this.currentConversationId) return;

    try {
      const options = {
        hostname: this.serverUrl,
        port: this.serverPort,
        path: `/conversation/${this.currentConversationId}`,
        method: 'GET'
      };

      return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
          let data = '';

          res.on('data', (chunk) => {
            data += chunk;
          });

          res.on('end', () => {
            try {
              const response = JSON.parse(data);
              if (response.messages && Array.isArray(response.messages)) {
                // Set lastMessageId to the highest current message ID
                const messageIds = response.messages.map((msg: any) => msg.id);
                if (messageIds.length > 0) {
                  this.lastMessageId = Math.max(...messageIds);
                }
              }
              resolve();
            } catch (error) {
              reject(error);
            }
          });
        });

        req.on('error', (e) => {
          reject(e);
        });

        req.end();
      });
    } catch (error) {
      // Silently handle errors
    }
  }

  private startPollingForDelayedThoughts(): void {
    // Clear any existing polling
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }

    console.log('\n⏳ Listening for character follow-up thoughts...\n');

    // Poll every 10 seconds for delayed thoughts
    this.pollingInterval = setInterval(async () => {
      try {
        await this.checkForDelayedThoughts();
      } catch (error) {
        // Silently handle errors to avoid cluttering the REPL
      }
    }, 10000);

    // Stop polling after 10 minutes
    setTimeout(() => {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval);
        this.pollingInterval = null;
        console.log('\n⏰ Stopped listening for character thoughts.\n');
      }
    }, 600000); // 10 minutes
  }

  private async checkForDelayedThoughts(): Promise<void> {
    if (!this.currentConversationId) return;

    const options = {
      hostname: this.serverUrl,
      port: this.serverPort,
      path: `/conversation/${this.currentConversationId}`,
      method: 'GET'
    };

    return new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let data = '';

        res.on('data', (chunk) => {
          data += chunk;
        });

        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            if (response.messages && Array.isArray(response.messages)) {
              // Filter for new messages since last check
              const newMessages = response.messages.filter((msg: any) =>
                msg.id > this.lastMessageId && msg.character !== 'user'
              );

              if (newMessages.length > 0) {
                console.log('\n💭 Character follow-up thoughts:');
                newMessages.forEach((msg: any) => {
                  console.log(`${msg.character}: ${msg.text}`);
                });
                console.log(''); // Empty line for spacing

                // Update last message ID
                this.lastMessageId = Math.max(...newMessages.map((msg: any) => msg.id));
              }
            }
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });

      req.on('error', (e) => {
        reject(e);
      });

      req.end();
    });
  }

  // Utility methods for enhanced REPL experience
  setPrompt(prompt: string): void {
    this.rl.setPrompt(prompt);
  }

  displayHelp(): void {
    console.log(`
📚 ForaChat Help:
- Ask questions about workplace skills like communication, leadership, teamwork
- Examples:
  • "How can I give better feedback to my team?"
  • "I'm having conflict with a coworker"
  • "How do I run more effective meetings?"
- Type "exit" or "quit" to end the session
- The team (Fora, Jan, Lou) will provide practical advice!
    `);
  }

  close(): void {
    this.rl.close();
  }
}
