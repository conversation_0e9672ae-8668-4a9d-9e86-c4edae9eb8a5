import { MessageInterface, ChatResponse } from '../models/types';
import { ChatService } from '../core/ChatService';

// Future SMS interface using Twilio or similar service
export class SMSInterface implements MessageInterface {
  private chatService: ChatService;
  private twilioClient?: any; // Would be Twilio client
  private phoneNumber: string;

  constructor(chatService: ChatService, phoneNumber: string) {
    this.chatService = chatService;
    this.phoneNumber = phoneNumber;
    // this.twilioClient = twilio(accountSid, authToken); // Future implementation
  }

  async sendMessage(message: string): Promise<void> {
    // Future implementation with Twilio
    console.log(`SMS would send to ${this.phoneNumber}: ${message}`);
    
    /*
    await this.twilioClient.messages.create({
      body: message,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: this.phoneNumber
    });
    */
  }

  async receiveMessage(): Promise<string> {
    // This would be handled by webhook in a real implementation
    throw new Error('SMS receiveMessage should be handled by webhook');
  }

  formatResponse(response: ChatResponse): string {
    // Format for SMS - shorter, more concise
    let formatted = `💼 ${response.theme}\n\n`;
    
    // Limit to first 2-3 messages for SMS length constraints
    const messagesToShow = response.reply.slice(0, 3);
    
    messagesToShow.forEach((message, index) => {
      // Use emojis to represent characters
      const characterEmoji = this.getCharacterEmoji(message.character);
      formatted += `${characterEmoji} ${message.text}\n\n`;
    });
    
    if (response.skills && response.skills.length > 0) {
      formatted += `🎯 Skills: ${response.skills.slice(0, 3).join(', ')}`;
    }
    
    // Ensure message isn't too long for SMS (160 char limit for single SMS)
    if (formatted.length > 1500) { // Leave room for multiple SMS segments
      formatted = formatted.substring(0, 1450) + '...';
    }
    
    return formatted;
  }

  private getCharacterEmoji(character: string): string {
    switch (character.toLowerCase()) {
      case 'fora':
        return '🌟'; // Enthusiastic leader
      case 'jan':
        return '📊'; // Data-driven
      case 'lou':
        return '🤝'; // People-focused
      default:
        return '💬';
    }
  }

  // Webhook handler for incoming SMS messages
  async handleIncomingMessage(from: string, body: string): Promise<void> {
    try {
      const result = await this.chatService.processUserMessage({ 
        text: body,
        userId: from 
      });
      
      const formattedResponse = this.formatResponse(result.response);
      await this.sendMessage(formattedResponse);
      
    } catch (error) {
      console.error(`Error processing SMS from ${from}:`, error);
      await this.sendMessage('Sorry, I had trouble processing your message. Please try again.');
    }
  }

  // Setup webhook endpoint (would be used in Express app)
  setupWebhook(app: any): void {
    app.post('/sms/webhook', async (req: any, res: any) => {
      const { From, Body } = req.body;
      
      try {
        await this.handleIncomingMessage(From, Body);
        res.status(200).send('OK');
      } catch (error) {
        console.error('SMS webhook error:', error);
        res.status(500).send('Error processing message');
      }
    });
  }

  // Utility methods for SMS management
  async sendWelcomeMessage(): Promise<void> {
    const welcomeMessage = `👋 Welcome to ForaChat! 

I'm here to help with workplace skills. Ask me about:
• Communication 💬
• Leadership 👑  
• Teamwork 🤝
• Conflict resolution ⚖️
• Feedback 📝

Just text me your question!`;

    await this.sendMessage(welcomeMessage);
  }

  async sendHelpMessage(): Promise<void> {
    const helpMessage = `📚 ForaChat Help:

Ask questions like:
• "How do I give better feedback?"
• "I'm having team conflict"
• "Leadership tips for new managers"

Get advice from Fora 🌟, Jan 📊, and Lou 🤝!`;

    await this.sendMessage(helpMessage);
  }
}
